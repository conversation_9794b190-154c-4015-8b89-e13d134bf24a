#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广东省各地市省级工程技术研究中心数量分布图表
重点对比湛江与省内发达城市的省级工程技术研究中心数量
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm
import csv

# 设置中文字体 - 使用楷体加粗
plt.rcParams['font.sans-serif'] = ['KaiTi', 'SimKai', 'STKaiti', 'Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'heavy'  # 全局字体更粗
plt.rcParams['axes.labelweight'] = 'heavy'  # 轴标签更粗
plt.rcParams['axes.titleweight'] = 'heavy'  # 标题更粗

def load_data_from_csv():
    """从CSV文件加载数据"""
    cities = []
    counts = []
    
    try:
        with open('2024年广东省各地市省级工程技术研究中心数量分布.csv', 'r', encoding='utf-8-sig') as file:
            csv_reader = csv.reader(file)
            for row in csv_reader:
                if len(row) >= 2 and row[0].strip() and row[1].strip():
                    city = row[0].strip()
                    count = int(row[1].strip())
                    cities.append(city)
                    counts.append(count)
    except FileNotFoundError:
        print("CSV文件未找到，使用默认数据")
        return get_default_data()
    except Exception as e:
        print(f"读取CSV文件出错: {e}，使用默认数据")
        return get_default_data()
    
    # 按数量从高到低排序
    sorted_data = sorted(zip(cities, counts), key=lambda x: x[1], reverse=True)
    cities, counts = zip(*sorted_data)
    
    return list(cities), list(counts)

def get_default_data():
    """默认数据（按数量从高到低排序）"""
    cities = ['广州', '深圳', '佛山', '东莞', '江门', '中山', '珠海', '惠州',
              '汕头', '肇庆', '清远', '韶关', '茂名', '湛江', '河源', '梅州',
              '揭阳', '潮州', '阳江', '云浮', '汕尾']
    counts = [2239, 1850, 947, 650, 437, 404, 402, 262,
              233, 205, 169, 120, 115, 107, 100, 92,
              87, 62, 55, 51, 21]
    return cities, counts

def create_engineering_center_chart():
    """创建省级工程技术研究中心数量分布图表"""
    
    # 加载数据
    all_cities, all_counts = load_data_from_csv()
    
    # 创建颜色列表
    colors = []
    for city in all_cities:
        if city == '湛江市' or city == '湛江':
            colors.append('#FF6B6B')  # 红色突出湛江
        else:
            colors.append('#4874CB')  # 蓝色表示其他城市

    # 创建图表，增大尺寸以容纳更大字体
    fig, ax = plt.subplots(figsize=(24, 14))

    # 创建柱状图，增加柱子间距
    bars = ax.bar(range(len(all_cities)), all_counts, color=colors, alpha=0.8,
                  edgecolor='black', linewidth=0.5, width=0.6)

    # 设置图表标题和标签，标题fontsize=42，柱状图数字fontsize=24，其他为36
    ax.set_title('2024年广东省各地市省级工程技术研究中心数量分布',
                fontsize=42, fontweight='heavy', pad=30, fontfamily='KaiTi')
    ax.set_xlabel('城市', fontsize=36, fontweight='heavy', labelpad=15, fontfamily='KaiTi')
    ax.set_ylabel('省级工程技术研究中心数量', fontsize=36, fontweight='heavy', labelpad=15, fontfamily='KaiTi')

    # 设置x轴标签，字体大小36
    ax.set_xticks(range(len(all_cities)))
    # 处理城市名称，去掉"市"字
    city_labels = [city.replace('市', '') for city in all_cities]
    ax.set_xticklabels(city_labels, rotation=45, ha='right', fontsize=36, fontweight='heavy', fontfamily='KaiTi')

    # 在每个柱子上添加数值标签，字体大小24
    for bar, count in zip(bars, all_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + max(all_counts) * 0.02,
                f'{count:,}', ha='center', va='bottom', fontsize=24, fontweight='heavy',
                fontfamily='KaiTi')

    # 设置y轴格式，字体大小36
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
    ax.tick_params(axis='y', labelsize=36, labelcolor='black')
    # 设置y轴标签字体为更粗
    for label in ax.get_yticklabels():
        label.set_fontweight('heavy')
        label.set_fontfamily('KaiTi')

    # 添加网格线
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)

    # 设置y轴范围，留更多空间给数值标签
    ax.set_ylim(0, max(all_counts) * 1.2)

    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('广东省省级工程技术研究中心分布图.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    # 显示图表
    plt.show()
    
    # 打印对比分析
    print("\n=== 广东省省级工程技术研究中心数量对比分析 ===")
    
    # 查找湛江的位置
    zhanjiang_index = -1
    zhanjiang_count = 0
    for i, city in enumerate(all_cities):
        if city == '湛江市' or city == '湛江':
            zhanjiang_index = i
            zhanjiang_count = all_counts[i]
            break
    
    if zhanjiang_index != -1:
        print(f"湛江市省级工程技术研究中心数量：{zhanjiang_count}个")
        print(f"湛江在全省21个地市中排名：第 {zhanjiang_index + 1} 位")
        
        print(f"\n数量最多的前5个城市：")
        for i in range(min(5, len(all_cities))):
            city_name = all_cities[i].replace('市', '')
            print(f"{i+1}. {city_name}：{all_counts[i]}个")
        
        print(f"\n湛江与数量最多城市（{all_cities[0].replace('市', '')}）的差距：{all_counts[0] - zhanjiang_count}个")
        
        # 统计不同数量区间的城市数量
        high_count = sum(1 for count in all_counts if count >= 500)
        medium_high_count = sum(1 for count in all_counts if 200 <= count < 500)
        medium_count = sum(1 for count in all_counts if 100 <= count < 200)
        low_count = sum(1 for count in all_counts if count < 100)
        
        print(f"\n数量分布统计：")
        print(f"≥500个：{high_count}个城市")
        print(f"200-499个：{medium_high_count}个城市")
        print(f"100-199个：{medium_count}个城市（湛江在此区间）")
        print(f"<100个：{low_count}个城市")
    else:
        print("未找到湛江市数据")

if __name__ == "__main__":
    print("正在生成广东省省级工程技术研究中心数量分布图表...")
    
    # 生成分布图表
    create_engineering_center_chart()
    
    print("\n图表生成完成！")
