#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
广东省高新技术企业数量对比图表
重点对比湛江与省内发达城市的高新技术企业数量
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib.font_manager as fm

# 设置中文字体 - 使用楷体加粗
plt.rcParams['font.sans-serif'] = ['KaiTi', 'SimKai', 'STKaiti', 'Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.weight'] = 'bold'  # 全局字体加粗
plt.rcParams['axes.labelweight'] = 'bold'  # 轴标签加粗
plt.rcParams['axes.titleweight'] = 'bold'  # 标题加粗

def create_detailed_comparison_chart():
    """创建更详细的对比图表，包含更多城市"""
    
    # 完整数据 - 2024年广东省各地市高新技术企业数量（按数量从高到低排序）
    all_cities = ['深圳', '广州', '东莞', '佛山', '惠州', '中山', '珠海', '江门',
                  '肇庆', '汕头', '清远', '湛江', '揭阳', '韶关', '河源', '潮州',
                  '梅州', '茂名', '云浮', '阳江', '汕尾']
    all_counts = [25983, 13727, 10347, 9683, 3695, 2976, 2973, 2471,
                  1315, 771, 588, 548, 474, 444, 357, 320,
                  257, 224, 195, 194, 137]
    
    # 创建颜色列表
    colors = []
    for city in all_cities:
        if city == '湛江':
            colors.append('#FF6B6B')  # 红色突出湛江
        else:
            colors.append('#4874CB')  # 蓝色表示其他城市

    # 创建图表，增大尺寸以容纳更大字体
    fig, ax = plt.subplots(figsize=(24, 14))

    # 创建柱状图，增加柱子间距
    bars = ax.bar(range(len(all_cities)), all_counts, color=colors, alpha=0.8,
                  edgecolor='black', linewidth=0.5, width=0.6)

    # 设置图表标题和标签，字体大小20，使用清晰字体
    ax.set_title('2024年广东省各地市高新技术企业数量分布\n（湛江在全省的位置）',
                fontsize=20, fontweight='bold', pad=30, fontfamily='Microsoft YaHei')
    ax.set_xlabel('城市', fontsize=20, fontweight='bold', labelpad=15, fontfamily='Microsoft YaHei')
    ax.set_ylabel('高新技术企业数量', fontsize=20, fontweight='bold', labelpad=15, fontfamily='Microsoft YaHei')

    # 设置x轴标签，字体大小20，使用清晰字体
    ax.set_xticks(range(len(all_cities)))
    ax.set_xticklabels(all_cities, rotation=45, ha='right', fontsize=20, fontweight='bold', fontfamily='Microsoft YaHei')

    # 在每个柱子上添加数值标签（所有城市都显示），字体大小20，使用清晰字体
    for bar, count in zip(bars, all_counts):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 500,
                f'{count:,}', ha='center', va='bottom', fontsize=20, fontweight='bold',
                fontfamily='Microsoft YaHei')

    # 设置y轴格式，字体大小20
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
    ax.tick_params(axis='y', labelsize=20, labelcolor='black')
    # 设置y轴标签字体为粗体
    for label in ax.get_yticklabels():
        label.set_fontweight('bold')
        label.set_fontfamily('Microsoft YaHei')

    # 添加网格线
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)

    # 设置y轴范围，留更多空间给数值标签
    ax.set_ylim(0, max(all_counts) * 1.2)


    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('广东省高新技术企业详细分布图.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    # 显示图表
    plt.show()

def create_tech_transfer_comparison_chart():
    """创建科技成果转化率对比图表"""

    # 科技成果转化率数据（按转化率从高到低排序）
    cities_transfer = ['珠海', '汕尾', '佛山', '清远', '中山', '江门', '阳江', '深圳',
                      '河源', '湛江', '东莞', '汕头', '广州', '肇庆', '惠州', '韶关',
                      '梅州', '茂名', '潮州', '揭阳', '云浮']
    transfer_rates = [32.28, 26.67, 15.80, 13.64, 11.80, 10.90, 10.00, 9.96,
                     9.26, 5.49, 5.14, 5.05, 4.32, 2.38, 1.16, 0.80,
                     0.39, 0.17, 0.00, 0.00, 0.00]

    # 创建颜色列表
    colors = []
    for city in cities_transfer:
        if city == '湛江':
            colors.append('#FF6B6B')  # 红色突出湛江
        else:
            colors.append('#4874CB')  # 青色表示转化率较高的城市


    # 创建图表，增大尺寸以容纳更大字体
    fig, ax = plt.subplots(figsize=(24, 14))

    # 创建柱状图，增加柱子间距
    bars = ax.bar(range(len(cities_transfer)), transfer_rates, color=colors, alpha=0.8,
                  edgecolor='black', linewidth=0.5, width=0.6)

    # 设置图表标题和标签，字体大小20，使用清晰字体
    ax.set_title('2024年广东省各地市科技成果转化率对比\n（湛江在全省的位置）',
                fontsize=20, fontweight='bold', pad=30, fontfamily='Microsoft YaHei')
    ax.set_xlabel('城市', fontsize=20, fontweight='bold', labelpad=15, fontfamily='Microsoft YaHei')
    ax.set_ylabel('科技成果转化率 (%)', fontsize=20, fontweight='bold', labelpad=15, fontfamily='Microsoft YaHei')

    # 设置x轴标签，字体大小20，使用清晰字体
    ax.set_xticks(range(len(cities_transfer)))
    ax.set_xticklabels(cities_transfer, rotation=45, ha='right', fontsize=20, fontweight='bold', fontfamily='Microsoft YaHei')

    # 在每个柱子上添加数值标签，字体大小20，使用清晰字体
    for bar, rate in zip(bars, transfer_rates):
        height = bar.get_height()
        if rate > 0:
            ax.text(bar.get_x() + bar.get_width()/2., height + 1.0,
                    f'{rate:.2f}', ha='center', va='bottom', fontsize=20, fontweight='bold',
                    fontfamily='Microsoft YaHei')
        else:
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    '0', ha='center', va='bottom', fontsize=20, fontweight='bold',
                    fontfamily='Microsoft YaHei')

    # 设置y轴格式，字体大小20
    ax.tick_params(axis='y', labelsize=20, labelcolor='black')
    # 设置y轴标签字体为粗体
    for label in ax.get_yticklabels():
        label.set_fontweight('bold')
        label.set_fontfamily('Microsoft YaHei')

    # 添加网格线
    ax.grid(True, alpha=0.3, axis='y')
    ax.set_axisbelow(True)

    # 设置y轴范围，留更多空间给数值标签
    ax.set_ylim(0, max(transfer_rates) * 1.2)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    plt.savefig('广东省科技成果转化率对比图.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    # 显示图表
    plt.show()

    # 打印对比分析
    print("\n=== 广东省科技成果转化率对比分析 ===")
    zhanjiang_index = cities_transfer.index('湛江')
    zhanjiang_rate = transfer_rates[zhanjiang_index]
    print(f"湛江市科技成果转化率：{zhanjiang_rate:.2f}%")
    print(f"湛江在全省21个地市中排名：第 {zhanjiang_index + 1} 位")

    print(f"\n转化率最高的前5个城市：")
    for i in range(5):
        print(f"{i+1}. {cities_transfer[i]}：{transfer_rates[i]:.2f}%")

    print(f"\n湛江与转化率最高城市（珠海）的差距：{transfer_rates[0] - zhanjiang_rate:.2f}个百分点")

    # 统计不同转化率区间的城市数量
    high_rate = sum(1 for rate in transfer_rates if rate >= 10)
    medium_rate = sum(1 for rate in transfer_rates if 5 <= rate < 10)
    low_rate = sum(1 for rate in transfer_rates if 0 < rate < 5)
    zero_rate = sum(1 for rate in transfer_rates if rate == 0)

    print(f"\n转化率分布统计：")
    print(f"转化率≥10%：{high_rate}个城市")
    print(f"转化率5-10%：{medium_rate}个城市（湛江在此区间）")
    print(f"转化率0-5%：{low_rate}个城市")
    print(f"转化率为0%：{zero_rate}个城市")

if __name__ == "__main__":
    print("正在生成广东省高新技术企业数量对比图表...")

    # 生成详细分布图表
    create_detailed_comparison_chart()

    print("\n正在生成科技成果转化率对比图表...")

    # 生成科技成果转化率对比图表
    create_tech_transfer_comparison_chart()

    print("\n所有图表生成完成！")
    print("已保存以下文件：")
    print("1. 广东省高新技术企业数量对比图.png")
    print("2. 广东省高新技术企业详细分布图.png")
    print("3. 广东省科技成果转化率对比图.png")
